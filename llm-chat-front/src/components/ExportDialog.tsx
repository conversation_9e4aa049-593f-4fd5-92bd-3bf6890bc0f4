import React, { useState } from 'react';
import styled from 'styled-components';
import { 
  Modal, 
  Form, 
  Radio, 
  Button, 
  message,
  Space,
  Typography
} from 'antd';
import { 
  DownloadOutlined, 
  FileTextOutlined,
  FilePdfOutlined,
  FileMarkdownOutlined
} from '@ant-design/icons';
import { ChatSession } from '../types';
import dayjs from 'dayjs';

const { Text } = Typography;

const ExportContainer = styled.div`
  padding: 16px;
`;

const FormatOption = styled.div`
  display: flex;
  align-items: center;
  padding: 12px;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    border-color: #1890ff;
    background: #f6f8fa;
  }
  
  &.selected {
    border-color: #1890ff;
    background: #e6f7ff;
  }
`;

const FormatIcon = styled.div`
  margin-right: 12px;
  font-size: 20px;
  color: #1890ff;
`;

const FormatInfo = styled.div`
  flex: 1;
  
  .format-title {
    font-weight: 500;
    margin-bottom: 4px;
  }
  
  .format-description {
    font-size: 12px;
    color: #666;
  }
`;

interface ExportDialogProps {
  visible: boolean;
  onClose: () => void;
  session: ChatSession;
}

const ExportDialog: React.FC<ExportDialogProps> = ({ visible, onClose, session }) => {
  const [format, setFormat] = useState('markdown');
  const [loading, setLoading] = useState(false);

  const exportFormats = [
    {
      value: 'markdown',
      title: 'Markdown',
      description: '适合文档和笔记，支持代码高亮',
      icon: <FileMarkdownOutlined />
    },
    {
      value: 'txt',
      title: '纯文本',
      description: '简单的文本格式，兼容性好',
      icon: <FileTextOutlined />
    },
    {
      value: 'json',
      title: 'JSON',
      description: '结构化数据，便于程序处理',
      icon: <FileTextOutlined />
    }
  ];

  const exportToMarkdown = (session: ChatSession): string => {
    let content = `# ${session.title}\n\n`;
    content += `**导出时间**: ${dayjs().format('YYYY-MM-DD HH:mm:ss')}\n`;
    content += `**对话时间**: ${dayjs(session.createdAt).format('YYYY-MM-DD HH:mm:ss')}\n`;
    content += `**消息数量**: ${session.messages.length}\n\n`;
    content += `---\n\n`;

    session.messages.forEach((message, index) => {
      const time = dayjs(message.timestamp).format('HH:mm:ss');
      const role = message.role === 'user' ? '👤 用户' : '🤖 AI';
      
      content += `### ${role} (${time})\n\n`;
      
      if (message.images && message.images.length > 0) {
        message.images.forEach(image => {
          content += `![图片](${image})\n\n`;
        });
      }
      
      content += `${message.content}\n\n`;
      content += `---\n\n`;
    });

    return content;
  };

  const exportToText = (session: ChatSession): string => {
    let content = `${session.title}\n`;
    content += `导出时间: ${dayjs().format('YYYY-MM-DD HH:mm:ss')}\n`;
    content += `对话时间: ${dayjs(session.createdAt).format('YYYY-MM-DD HH:mm:ss')}\n`;
    content += `消息数量: ${session.messages.length}\n\n`;

    session.messages.forEach((message, index) => {
      const time = dayjs(message.timestamp).format('HH:mm:ss');
      const role = message.role === 'user' ? '用户' : 'AI';
      
      content += `[${time}] ${role}:\n`;
      
      if (message.images && message.images.length > 0) {
        message.images.forEach(image => {
          content += `[图片] ${image}\n`;
        });
      }
      
      content += `${message.content}\n\n`;
    });

    return content;
  };

  const exportToJson = (session: ChatSession): string => {
    return JSON.stringify({
      title: session.title,
      id: session.id,
      createdAt: session.createdAt,
      updatedAt: session.updatedAt,
      messages: session.messages,
      exportTime: new Date().toISOString()
    }, null, 2);
  };

  const handleExport = async () => {
    try {
      setLoading(true);
      
      let content = '';
      let filename = '';
      let mimeType = '';
      
      switch (format) {
        case 'markdown':
          content = exportToMarkdown(session);
          filename = `${session.title.replace(/[^a-zA-Z0-9]/g, '_')}.md`;
          mimeType = 'text/markdown';
          break;
        case 'txt':
          content = exportToText(session);
          filename = `${session.title.replace(/[^a-zA-Z0-9]/g, '_')}.txt`;
          mimeType = 'text/plain';
          break;
        case 'json':
          content = exportToJson(session);
          filename = `${session.title.replace(/[^a-zA-Z0-9]/g, '_')}.json`;
          mimeType = 'application/json';
          break;
      }

      // 创建下载链接
      const blob = new Blob([content], { type: mimeType });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      message.success('导出成功！');
      onClose();
    } catch (error) {
      message.error('导出失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title={
        <Space>
          <DownloadOutlined />
          导出对话
        </Space>
      }
      open={visible}
      onCancel={onClose}
      footer={[
        <Button key="cancel" onClick={onClose}>
          取消
        </Button>,
        <Button 
          key="export" 
          type="primary" 
          icon={<DownloadOutlined />}
          loading={loading}
          onClick={handleExport}
        >
          导出
        </Button>,
      ]}
      width={500}
    >
      <ExportContainer>
        <div style={{ marginBottom: 16 }}>
          <Text>选择导出格式：</Text>
        </div>
        
        <Form.Item>
          <Radio.Group 
            value={format} 
            onChange={(e) => setFormat(e.target.value)}
            style={{ width: '100%' }}
          >
            {exportFormats.map(formatOption => (
              <FormatOption
                key={formatOption.value}
                className={format === formatOption.value ? 'selected' : ''}
                onClick={() => setFormat(formatOption.value)}
              >
                <FormatIcon>{formatOption.icon}</FormatIcon>
                <FormatInfo>
                  <div className="format-title">{formatOption.title}</div>
                  <div className="format-description">{formatOption.description}</div>
                </FormatInfo>
                <Radio value={formatOption.value} />
              </FormatOption>
            ))}
          </Radio.Group>
        </Form.Item>
        
        <div style={{ marginTop: 16, padding: 12, background: '#f6f8fa', borderRadius: 6 }}>
          <Text type="secondary">
            将导出 "{session.title}" 对话，包含 {session.messages.length} 条消息
          </Text>
        </div>
      </ExportContainer>
    </Modal>
  );
};

export default ExportDialog; 