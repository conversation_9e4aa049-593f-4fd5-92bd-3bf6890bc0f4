import axios from 'axios'
import { config } from '../config/index.js'

// Dify API 配置
const DIFY_API_BASE = config.dify.apiBase || 'https://api.dify.ai/v1'
const API_KEY = config.dify.apiKey || ''
const USER_ID = config.dify.userId || 'user-bba-dify-frontend'

// 创建 axios 实例
const apiClient = axios.create({
  baseURL: DIFY_API_BASE,
  headers: {
    'Authorization': `Bearer ${API_KEY}`,
    'Content-Type': 'application/json'
  },
  timeout: config.dify.timeout || 30000
})

// 聊天服务类
class ChatService {
  constructor() {
    this.conversationId = null
    this.userId = USER_ID
    this.currentTaskId = null
    console.log('🔧 ChatService 初始化，用户ID:', this.userId)
  }

  /**
   * 发送消息到 Dify API
   * @param {string} message - 用户消息
   * @param {function} onChunk - 流式响应回调函数
   * @param {Array} files - 上传的文件列表
   * @returns {Promise}
   */
  async sendMessage(message, onChunk, files = []) {
    // 检查 API Key
    if (!API_KEY) {
      console.warn('未配置 Dify API Key，使用模拟模式')
      return this.simulateResponse(message, onChunk)
    }

    console.log('🚀 发送消息到 Dify API:', message)
    console.log('📋 API 配置:', { apiBase: DIFY_API_BASE, hasApiKey: !!API_KEY })

    try {
      const requestData = {
        inputs: {},
        query: message,
        response_mode: 'streaming',
        user: this.userId,
        auto_generate_name: true
      }

      // 如果有对话ID，继续之前的对话
      if (this.conversationId) {
        requestData.conversation_id = this.conversationId
        console.log('📝 继续对话:', this.conversationId)
      }

      // 如果有文件，添加到请求中
      if (files && files.length > 0) {
        requestData.files = files
        console.log('📁 包含文件:', files.length)
      }

      console.log('📤 请求数据:', requestData)

      // 使用 fetch 进行 SSE 请求
      const response = await fetch(`${DIFY_API_BASE}/chat-messages`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${API_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      })

      console.log('📥 响应状态:', response.status, response.statusText)

      if (!response.ok) {
        const errorText = await response.text()
        console.error('❌ API 错误响应:', errorText)
        throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`)
      }

      // 检查响应模式
      if (requestData.response_mode === 'blocking') {
        return this.handleBlockingResponse(response, onChunk)
      } else {
        return this.handleStreamResponse(response, onChunk)
      }
    } catch (error) {
      console.error('❌ API 调用失败:', error)

      // 如果是网络错误或 API 不可用，使用模拟响应
      if (error.name === 'TypeError' || error.message.includes('fetch') || error.message.includes('HTTP 4')) {
        console.warn('⚠️ 切换到模拟模式:', error.message)
        return this.simulateResponse(message, onChunk)
      }

      throw error
    }
  }

  /**
   * 处理阻塞模式响应
   * @param {Response} response - fetch 响应对象
   * @param {function} onChunk - 流式响应回调函数
   * @returns {Promise}
   */
  async handleBlockingResponse(response, onChunk) {
    console.log('🔒 处理阻塞模式响应')

    try {
      const data = await response.json()
      console.log('📦 收到阻塞响应:', data)

      // 更新对话ID和任务ID
      if (data.conversation_id) {
        this.conversationId = data.conversation_id
        console.log('💾 更新对话ID:', this.conversationId)
      }
      if (data.task_id) {
        this.currentTaskId = data.task_id
        console.log('🆔 更新任务ID:', this.currentTaskId)
      }

      // 获取答案内容
      if (data.answer) {
        console.log('💬 收到完整答案:', data.answer)

        // 模拟流式输出效果
        const answer = data.answer
        let index = 0
        const chunkSize = 3 // 每次发送3个字符

        const sendChunk = () => {
          if (index < answer.length) {
            const chunk = answer.slice(index, index + chunkSize)
            console.log('📤 发送 chunk:', chunk)
            onChunk(chunk)
            index += chunkSize
            setTimeout(sendChunk, 50) // 50ms 延迟模拟打字效果
          }
        }

        sendChunk()
      } else {
        console.warn('⚠️ 阻塞响应中没有找到答案内容')
      }

    } catch (error) {
      console.error('❌ 阻塞响应处理错误:', error)
      throw error
    }
  }

  /**
   * 处理流式响应
   * @param {Response} response - fetch 响应对象
   * @param {function} onChunk - 流式响应回调函数
   * @returns {Promise}
   */
  async handleStreamResponse(response, onChunk) {
    console.log('🌊 开始处理流式响应')
    const reader = response.body.getReader()
    const decoder = new TextDecoder()
    let buffer = ''
    let accumulatedContent = '' // 累积的消息内容

    return new Promise((resolve, reject) => {
      const processStream = async () => {
        try {
          while (true) {
            const { done, value } = await reader.read()

            if (done) {
              console.log('✅ 流式响应结束')
              resolve()
              break
            }

            const chunk = decoder.decode(value, { stream: true })
            console.log('🔄 收到原始数据块:', chunk)
            buffer += chunk
            const lines = buffer.split('\n')
            buffer = lines.pop() || '' // 保留不完整的行

            for (const line of lines) {
              console.log('📄 处理行:', line)

              if (line.startsWith('data: ')) {
                const data = line.slice(6).trim()

                if (data === '') {
                  console.log('⏭️ 跳过空数据行')
                  continue
                }

                console.log('📨 收到 SSE 数据:', data)

                try {
                  const parsed = JSON.parse(data)
                  console.log('📋 解析的事件:', parsed.event, parsed)

                  // 累积消息内容
                  if ((parsed.event === 'message' || parsed.event === 'agent_message') && parsed.answer) {
                    accumulatedContent += parsed.answer
                    console.log('📝 累积消息内容:', accumulatedContent)
                  }

                  await this.handleStreamEvent(parsed, onChunk, accumulatedContent)

                  if (parsed.event === 'message_end' || parsed.event === 'error') {
                    console.log('🏁 流式响应完成:', parsed.event)
                    console.log('📝 最终累积内容:', accumulatedContent)

                    // 如果没有通过流式事件收到内容，但累积了内容，则发送累积的内容
                    if (accumulatedContent && accumulatedContent.trim()) {
                      console.log('📤 发送累积的完整内容')
                      onChunk(accumulatedContent)
                    }

                    resolve()
                    return
                  }
                } catch (e) {
                  console.warn('⚠️ 解析 SSE 数据失败:', data, e)
                }
              } else if (line.trim()) {
                console.log('❓ 非 SSE 格式的行:', line)
              }
            }
          }
        } catch (error) {
          console.error('❌ 流式响应处理错误:', error)
          reject(error)
        }
      }

      processStream()
    })
  }

  /**
   * 处理流式事件
   * @param {Object} event - SSE 事件数据
   * @param {function} onChunk - 流式响应回调函数
   * @param {string} accumulatedContent - 累积的消息内容
   */
  async handleStreamEvent(event, onChunk, accumulatedContent = '') {
    console.log('🎯 处理流式事件:', event.event, event)

    // 更新对话ID和任务ID
    if (event.conversation_id) {
      this.conversationId = event.conversation_id
      console.log('💾 更新对话ID:', this.conversationId)
    }
    if (event.task_id) {
      this.currentTaskId = event.task_id
      console.log('🆔 更新任务ID:', this.currentTaskId)
    }

    switch (event.event) {
      case 'message':
      case 'agent_message':
        // 处理消息内容
        if (event.answer) {
          // 确保正确解码 Unicode 字符
          let decodedAnswer = event.answer
          try {
            // 如果包含 Unicode 转义序列，进行解码
            if (decodedAnswer.includes('\\u')) {
              decodedAnswer = JSON.parse(`"${decodedAnswer}"`)
            }
          } catch (e) {
            // 如果解码失败，使用原始内容
            console.warn('Unicode 解码失败，使用原始内容:', e)
          }

          console.log('💬 收到消息内容 (原始):', event.answer)
          console.log('💬 收到消息内容 (解码):', decodedAnswer)
          console.log('💬 调用 onChunk 函数:', typeof onChunk)

          onChunk(decodedAnswer)
        }
        break

      case 'agent_thought':
        // Agent 思考过程（可选择是否显示）
        if (event.thought) {
          console.log('🤔 Agent 思考:', event.thought)
        }
        break

      case 'message_file':
        // 处理文件消息
        console.log('📁 收到文件:', event)
        break

      case 'message_end':
        // 消息结束 - 检查是否有完整的答案内容
        console.log('🏁 消息结束，完整事件:', event)
        console.log('🏁 消息结束，用量信息:', event.metadata?.usage)

        // 如果 message_end 事件包含完整答案，直接显示
        if (event.answer && event.answer.trim()) {
          console.log('📝 从 message_end 获取完整答案:', event.answer)

          // 确保正确解码 Unicode 字符
          let decodedAnswer = event.answer
          try {
            if (decodedAnswer.includes('\\u')) {
              decodedAnswer = JSON.parse(`"${decodedAnswer}"`)
            }
          } catch (e) {
            console.warn('Unicode 解码失败，使用原始内容:', e)
          }

          console.log('📝 解码后的答案:', decodedAnswer)
          onChunk(decodedAnswer)
        } else {
          console.warn('⚠️ message_end 事件中没有找到答案内容')
        }
        break

      case 'error':
        // 错误处理
        console.error('❌ 流式响应错误:', event)
        throw new Error(event.message || '未知错误')

      case 'ping':
        // 心跳包，保持连接
        console.log('💓 收到心跳包')
        break

      default:
        console.log('❓ 未处理的事件类型:', event.event, event)
    }
  }

  /**
   * 停止当前响应
   * @returns {Promise}
   */
  async stopResponse() {
    if (!this.currentTaskId) {
      return { result: 'no_active_task' }
    }

    try {
      const response = await apiClient.post(`/chat-messages/${this.currentTaskId}/stop`, {
        user: this.userId
      })

      this.currentTaskId = null
      return response.data
    } catch (error) {
      console.error('停止响应失败:', error)
      throw error
    }
  }

  /**
   * 上传文件
   * @param {File} file - 要上传的文件
   * @returns {Promise<Object>} 文件信息
   */
  async uploadFile(file) {
    if (!API_KEY) {
      throw new Error('未配置 API Key，无法上传文件')
    }

    const formData = new FormData()
    formData.append('file', file)
    formData.append('user', this.userId)

    try {
      const response = await apiClient.post('/files/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })

      return response.data
    } catch (error) {
      console.error('文件上传失败:', error)
      throw error
    }
  }

  /**
   * 消息反馈
   * @param {string} messageId - 消息ID
   * @param {string} rating - 评分 ('like' | 'dislike' | null)
   * @param {string} content - 反馈内容
   * @returns {Promise}
   */
  async sendFeedback(messageId, rating, content = '') {
    if (!API_KEY) {
      console.warn('未配置 API Key，无法发送反馈')
      return { result: 'no_api_key' }
    }

    try {
      const response = await apiClient.post(`/messages/${messageId}/feedbacks`, {
        rating,
        user: this.userId,
        content
      })

      return response.data
    } catch (error) {
      console.error('发送反馈失败:', error)
      throw error
    }
  }

  /**
   * 获取建议问题
   * @param {string} messageId - 消息ID
   * @returns {Promise<Array>}
   */
  async getSuggestedQuestions(messageId) {
    if (!API_KEY) {
      return []
    }

    try {
      const response = await apiClient.get(`/messages/${messageId}/suggested`, {
        params: {
          user: this.userId
        }
      })

      return response.data.data || []
    } catch (error) {
      console.error('获取建议问题失败:', error)
      return []
    }
  }

  /**
   * 模拟 AI 响应（用于演示或 API 不可用时）
   * @param {string} message - 用户消息
   * @param {function} onChunk - 流式响应回调函数
   * @returns {Promise}
   */
  async simulateResponse(message, onChunk) {
    const responses = [
      '感谢您的问题！我是基于 Dify 的 AI 助手。',
      '这是一个很有趣的问题。让我为您详细解答...',
      '根据您的描述，我建议您可以考虑以下几个方面：\n\n1. 首先分析问题的核心\n2. 制定解决方案\n3. 逐步实施和优化',
      '我理解您的需求。作为 AI 助手，我会尽力为您提供准确和有用的信息。',
      '这个问题涉及多个方面，让我逐一为您分析：\n\n**技术层面：**\n- 需要考虑性能优化\n- 用户体验设计\n- 安全性保障\n\n**实施建议：**\n- 采用渐进式开发\n- 持续测试和改进'
    ]

    const response = responses[Math.floor(Math.random() * responses.length)]

    // 模拟流式输出
    return new Promise((resolve) => {
      let index = 0
      const interval = setInterval(() => {
        if (index < response.length) {
          const chunk = response.slice(index, index + Math.random() * 5 + 1)
          onChunk(chunk)
          index += chunk.length
        } else {
          clearInterval(interval)
          resolve()
        }
      }, 50 + Math.random() * 100) // 随机延迟模拟真实打字效果
    })
  }

  /**
   * 获取会话历史消息
   * @param {string} conversationId - 会话ID
   * @param {string} firstId - 当前页第一条消息ID
   * @param {number} limit - 返回条数
   * @returns {Promise<Object>}
   */
  async getConversationMessages(conversationId = null, firstId = null, limit = 20) {
    const targetConversationId = conversationId || this.conversationId

    if (!targetConversationId || !API_KEY) {
      return { data: [], has_more: false, limit: 0 }
    }

    try {
      const params = {
        user: this.userId,
        limit
      }

      if (firstId) {
        params.first_id = firstId
      }

      const response = await apiClient.get(`/messages`, {
        params: {
          ...params,
          conversation_id: targetConversationId
        }
      })

      console.log('📜 获取会话消息成功:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ 获取会话消息失败:', error)
      return { data: [], has_more: false, limit: 0 }
    }
  }

  /**
   * 获取会话列表
   * @param {string} lastId - 最后一条记录ID
   * @param {number} limit - 返回条数
   * @param {string} sortBy - 排序字段
   * @returns {Promise<Object>}
   */
  async getConversations(lastId = null, limit = 20, sortBy = '-updated_at') {
    console.log('🔍 开始获取会话列表...')
    console.log('📋 API 配置检查:', {
      apiBase: DIFY_API_BASE,
      hasApiKey: !!API_KEY,
      apiKeyPrefix: API_KEY ? API_KEY.substring(0, 10) + '...' : 'null',
      userId: this.userId
    })

    if (!API_KEY) {
      console.warn('⚠️ 未配置 API Key，返回空数据')
      return { data: [], has_more: false, limit: 0 }
    }

    try {
      const params = {
        user: this.userId,
        limit,
        sort_by: sortBy
      }

      if (lastId) {
        params.last_id = lastId
      }

      console.log('📤 发送请求参数:', params)
      console.log('🌐 请求 URL:', `${DIFY_API_BASE}/conversations`)

      const response = await apiClient.get('/conversations', { params })

      console.log('✅ 获取会话列表成功:', response.data)
      console.log('📊 响应状态:', response.status)
      console.log('📋 会话数量:', response.data?.data?.length || 0)

      return response.data
    } catch (error) {
      console.error('❌ 获取会话列表失败:', error)

      // 详细错误信息
      if (error.response) {
        console.error('📋 响应错误:', {
          status: error.response.status,
          statusText: error.response.statusText,
          data: error.response.data,
          headers: error.response.headers
        })
      } else if (error.request) {
        console.error('📋 请求错误:', {
          message: '没有收到响应',
          request: error.request
        })
      } else {
        console.error('📋 配置错误:', error.message)
      }

      // 抛出错误以便上层处理
      throw error
    }
  }

  /**
   * 删除会话
   * @param {string} conversationId - 会话ID
   * @returns {Promise<boolean>}
   */
  async deleteConversation(conversationId) {
    if (!API_KEY) {
      console.warn('未配置 API Key，无法删除会话')
      return false
    }

    try {
      await apiClient.delete(`/conversations/${conversationId}`, {
        data: {
          user: this.userId
        }
      })

      console.log('🗑️ 删除会话成功:', conversationId)

      // 如果删除的是当前会话，重置会话ID
      if (conversationId === this.conversationId) {
        this.resetConversation()
      }

      return true
    } catch (error) {
      console.error('❌ 删除会话失败:', error)
      return false
    }
  }

  /**
   * 重命名会话
   * @param {string} conversationId - 会话ID
   * @param {string} name - 新名称
   * @param {boolean} autoGenerate - 是否自动生成
   * @returns {Promise<Object|null>}
   */
  async renameConversation(conversationId, name = '', autoGenerate = false) {
    if (!API_KEY) {
      console.warn('未配置 API Key，无法重命名会话')
      return null
    }

    try {
      const response = await apiClient.post(`/conversations/${conversationId}/name`, {
        name,
        auto_generate: autoGenerate,
        user: this.userId
      })

      console.log('✏️ 重命名会话成功:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ 重命名会话失败:', error)
      return null
    }
  }

  /**
   * 重置对话
   */
  resetConversation() {
    this.conversationId = null
    this.currentTaskId = null
  }

  /**
   * 获取应用信息
   * @returns {Promise<Object>}
   */
  async getAppInfo() {
    if (!API_KEY) {
      return {
        opening_statement: '您好！我是 AI 助手，有什么可以帮助您的吗？',
        suggested_questions: [
          '你好，请介绍一下自己',
          '帮我写一个 Vue 组件',
          '解释一下这段代码',
          '如何优化网站性能？'
        ]
      }
    }

    try {
      const response = await apiClient.get('/parameters', {
        params: {
          user: this.userId
        }
      })

      return response.data
    } catch (error) {
      console.error('获取应用信息失败:', error)
      return {
        opening_statement: '您好！我是 AI 助手，有什么可以帮助您的吗？',
        suggested_questions: [
          '你好，请介绍一下自己',
          '帮我写一个 Vue 组件',
          '解释一下这段代码',
          '如何优化网站性能？'
        ]
      }
    }
  }

  /**
   * 语音转文字
   * @param {File} audioFile - 音频文件
   * @returns {Promise<string>}
   */
  async audioToText(audioFile) {
    if (!API_KEY) {
      throw new Error('未配置 API Key，无法使用语音转文字功能')
    }

    const formData = new FormData()
    formData.append('file', audioFile)
    formData.append('user', this.userId)

    try {
      const response = await apiClient.post('/audio-to-text', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })

      console.log('🎤 语音转文字成功:', response.data.text)
      return response.data.text
    } catch (error) {
      console.error('❌ 语音转文字失败:', error)
      throw error
    }
  }

  /**
   * 文字转语音
   * @param {string} text - 文本内容
   * @param {string} messageId - 消息ID（可选）
   * @returns {Promise<Blob>}
   */
  async textToAudio(text = '', messageId = '') {
    if (!API_KEY) {
      throw new Error('未配置 API Key，无法使用文字转语音功能')
    }

    const formData = new FormData()
    formData.append('user', this.userId)

    if (messageId) {
      formData.append('message_id', messageId)
    } else {
      formData.append('text', text)
    }

    try {
      const response = await apiClient.post('/text-to-audio', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        responseType: 'blob'
      })

      console.log('🔊 文字转语音成功')
      return response.data
    } catch (error) {
      console.error('❌ 文字转语音失败:', error)
      throw error
    }
  }

  /**
   * 获取应用基本信息
   * @returns {Promise<Object>}
   */
  async getAppInfo() {
    if (!API_KEY) {
      return {
        name: 'Dify Chat Interface',
        description: '基于 Dify 的智能对话助手',
        mode: 'chat',
        author_name: 'Dify'
      }
    }

    try {
      const response = await apiClient.get('/info')
      console.log('ℹ️ 获取应用信息成功:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ 获取应用信息失败:', error)
      return {
        name: 'Dify Chat Interface',
        description: '基于 Dify 的智能对话助手',
        mode: 'chat',
        author_name: 'Dify'
      }
    }
  }

  /**
   * 获取应用参数配置
   * @returns {Promise<Object>}
   */
  async getAppParameters() {
    if (!API_KEY) {
      return {
        opening_statement: '您好！我是 AI 助手，有什么可以帮助您的吗？',
        suggested_questions: [
          '你好，请介绍一下自己',
          '帮我写一个 Vue 组件',
          '解释一下这段代码',
          '如何优化网站性能？'
        ],
        speech_to_text: { enabled: false },
        text_to_speech: { enabled: false },
        file_upload: {
          image: { enabled: true, number_limits: 3 }
        }
      }
    }

    try {
      const response = await apiClient.get('/parameters')
      console.log('⚙️ 获取应用参数成功:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ 获取应用参数失败:', error)
      return {
        opening_statement: '您好！我是 AI 助手，有什么可以帮助您的吗？',
        suggested_questions: [
          '你好，请介绍一下自己',
          '帮我写一个 Vue 组件',
          '解释一下这段代码',
          '如何优化网站性能？'
        ]
      }
    }
  }

  /**
   * 获取当前配置状态
   * @returns {Object}
   */
  getStatus() {
    return {
      hasApiKey: !!API_KEY,
      apiBase: DIFY_API_BASE,
      userId: this.userId,
      conversationId: this.conversationId,
      currentTaskId: this.currentTaskId
    }
  }
}

// 导出单例实例
export const chatService = new ChatService()

// 导出类以便测试
export { ChatService }
