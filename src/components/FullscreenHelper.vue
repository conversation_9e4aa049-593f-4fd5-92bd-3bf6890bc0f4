<template>
  <div class="fullscreen-helper" v-if="showHelper">
    <div class="helper-content">
      <el-icon :size="24"><FullScreen /></el-icon>
      <p>按 F11 进入全屏模式获得更好的体验</p>
      <div class="helper-actions">
        <el-button size="small" @click="enterFullscreen">进入全屏</el-button>
        <el-button size="small" text @click="dismissHelper">不再提示</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { FullScreen } from '@element-plus/icons-vue'

const showHelper = ref(false)

const emit = defineEmits(['enter-fullscreen'])

// 进入全屏
const enterFullscreen = () => {
  emit('enter-fullscreen')
  dismissHelper()
}

// 关闭提示
const dismissHelper = () => {
  showHelper.value = false
  localStorage.setItem('fullscreen-helper-dismissed', 'true')
}

onMounted(() => {
  // 检查是否已经关闭过提示
  const dismissed = localStorage.getItem('fullscreen-helper-dismissed')
  if (!dismissed && !document.fullscreenElement) {
    // 延迟显示提示
    setTimeout(() => {
      showHelper.value = true
    }, 3000)
  }
})
</script>

<style scoped>
.fullscreen-helper {
  position: fixed;
  top: 20px;
  right: 20px;
  background: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border: 1px solid #e4e7ed;
  z-index: 1000;
  max-width: 280px;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.helper-content {
  text-align: center;
}

.helper-content p {
  margin: 12px 0;
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
}

.helper-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
  margin-top: 12px;
}

/* 移动端隐藏 */
@media (max-width: 768px) {
  .fullscreen-helper {
    display: none;
  }
}
</style>
