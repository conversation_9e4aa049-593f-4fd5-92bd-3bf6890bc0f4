<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.quantitative.trading.mapper.L2TradeDataMapper">

    <!-- 结果映射 -->
    <resultMap id="L2TradeDataResultMap" type="L2TradeData">
        <id column="id" property="id"/>
        <result column="tran_id" property="tranId"/>
        <result column="trade_time" property="tradeTime"/>
        <result column="price" property="price"/>
        <result column="volume" property="volume"/>
        <result column="sale_order_volume" property="saleOrderVolume"/>
        <result column="buy_order_volume" property="buyOrderVolume"/>
        <result column="trade_type" property="tradeType"/>
        <result column="sale_order_id" property="saleOrderId"/>
        <result column="sale_order_price" property="saleOrderPrice"/>
        <result column="buy_order_id" property="buyOrderId"/>
        <result column="buy_order_price" property="buyOrderPrice"/>
        <result column="stock_code" property="stockCode"/>
        <result column="trade_date" property="tradeDate"/>
        <result column="created_at" property="createdAt"/>
    </resultMap>

    <!-- 插入单条数据 -->
    <insert id="insert" parameterType="L2TradeData" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO l2_trade_data (
            tran_id, trade_time, price, volume, sale_order_volume, buy_order_volume,
            trade_type, sale_order_id, sale_order_price, buy_order_id, buy_order_price,
            stock_code, trade_date
        ) VALUES (
            #{tranId}, #{tradeTime}, #{price}, #{volume}, #{saleOrderVolume}, #{buyOrderVolume},
            #{tradeType}, #{saleOrderId}, #{saleOrderPrice}, #{buyOrderId}, #{buyOrderPrice},
            #{stockCode}, #{tradeDate}
        )
    </insert>

    <!-- 批量插入 -->
    <insert id="batchInsert" parameterType="list">
        INSERT INTO l2_trade_data (
            tran_id, trade_time, price, volume, sale_order_volume, buy_order_volume,
            trade_type, sale_order_id, sale_order_price, buy_order_id, buy_order_price,
            stock_code, trade_date
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.tranId}, #{item.tradeTime}, #{item.price}, #{item.volume}, 
                #{item.saleOrderVolume}, #{item.buyOrderVolume}, #{item.tradeType}, 
                #{item.saleOrderId}, #{item.saleOrderPrice}, #{item.buyOrderId}, 
                #{item.buyOrderPrice}, #{item.stockCode}, #{item.tradeDate}
            )
        </foreach>
    </insert>

    <!-- 根据ID查询 -->
    <select id="selectById" parameterType="long" resultMap="L2TradeDataResultMap">
        SELECT * FROM l2_trade_data WHERE id = #{id}
    </select>

    <!-- 根据股票代码查询 -->
    <select id="selectByStockCode" parameterType="string" resultMap="L2TradeDataResultMap">
        SELECT * FROM l2_trade_data 
        WHERE stock_code = #{stockCode}
        ORDER BY trade_time ASC
    </select>

    <!-- 根据股票代码和交易日期查询 -->
    <select id="selectByStockCodeAndDate" resultMap="L2TradeDataResultMap">
        SELECT * FROM l2_trade_data 
        WHERE stock_code = #{stockCode} AND trade_date = #{tradeDate}
        ORDER BY trade_time ASC
    </select>

    <!-- 统计指定股票代码的数据总数 -->
    <select id="countByStockCode" parameterType="string" resultType="long">
        SELECT COUNT(*) FROM l2_trade_data WHERE stock_code = #{stockCode}
    </select>

    <!-- 删除指定股票代码和日期的数据 -->
    <delete id="deleteByStockCodeAndDate">
        DELETE FROM l2_trade_data
        WHERE stock_code = #{stockCode} AND trade_date = #{tradeDate}
    </delete>

    <!-- 检查数据是否已存在 -->
    <select id="checkDataExists" resultType="int">
        SELECT COUNT(*) FROM l2_trade_data
        WHERE tran_id = #{tranId} AND stock_code = #{stockCode} AND trade_date = #{tradeDate}
    </select>

    <!-- 批量插入（忽略重复数据） -->
    <insert id="batchInsertIgnoreDuplicate" parameterType="list">
        INSERT INTO l2_trade_data (
            tran_id, trade_time, price, volume, sale_order_volume, buy_order_volume,
            trade_type, sale_order_id, sale_order_price, buy_order_id, buy_order_price,
            stock_code, trade_date
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.tranId}, #{item.tradeTime}, #{item.price}, #{item.volume},
                #{item.saleOrderVolume}, #{item.buyOrderVolume}, #{item.tradeType},
                #{item.saleOrderId}, #{item.saleOrderPrice}, #{item.buyOrderId},
                #{item.buyOrderPrice}, #{item.stockCode}, #{item.tradeDate}
            )
        </foreach>
        ON CONFLICT (tran_id, stock_code, trade_date) DO NOTHING
    </insert>

    <!-- 统计重复记录数 -->
    <select id="countDuplicateRecords" resultType="int">
        SELECT COUNT(*) FROM (
            SELECT tran_id, stock_code, trade_date, COUNT(*) as cnt
            FROM l2_trade_data
            WHERE stock_code = #{stockCode} AND trade_date = #{tradeDate}
            GROUP BY tran_id, stock_code, trade_date
            HAVING COUNT(*) > 1
        ) duplicates
    </select>

</mapper>
