-- 创建L2交易数据表
CREATE TABLE IF NOT EXISTS l2_trade_data (
    id BIGSERIAL PRIMARY KEY,
    tran_id BIGINT NOT NULL,
    trade_time TIME NOT NULL,
    price DECIMAL(10, 4) NOT NULL,
    volume INTEGER NOT NULL,
    sale_order_volume INTEGER NOT NULL,
    buy_order_volume INTEGER NOT NULL,
    trade_type CHAR(1) NOT NULL,
    sale_order_id BIGINT NOT NULL,
    sale_order_price DECIMAL(10, 4) NOT NULL,
    buy_order_id BIGINT NOT NULL,
    buy_order_price DECIMAL(10, 4) NOT NULL,
    stock_code VARCHAR(10) NOT NULL,
    trade_date DATE NOT NULL DEFAULT CURRENT_DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_l2_trade_stock_code ON l2_trade_data(stock_code);
CREATE INDEX IF NOT EXISTS idx_l2_trade_date ON l2_trade_data(trade_date);
CREATE INDEX IF NOT EXISTS idx_l2_trade_time ON l2_trade_data(trade_time);
CREATE INDEX IF NOT EXISTS idx_l2_trade_tran_id ON l2_trade_data(tran_id);

-- 添加注释
COMMENT ON TABLE l2_trade_data IS 'L2交易明细数据表';
COMMENT ON COLUMN l2_trade_data.tran_id IS '交易ID';
COMMENT ON COLUMN l2_trade_data.trade_time IS '交易时间';
COMMENT ON COLUMN l2_trade_data.price IS '成交价格';
COMMENT ON COLUMN l2_trade_data.volume IS '成交量';
COMMENT ON COLUMN l2_trade_data.sale_order_volume IS '卖单量';
COMMENT ON COLUMN l2_trade_data.buy_order_volume IS '买单量';
COMMENT ON COLUMN l2_trade_data.trade_type IS '交易类型(B/S)';
COMMENT ON COLUMN l2_trade_data.sale_order_id IS '卖单ID';
COMMENT ON COLUMN l2_trade_data.sale_order_price IS '卖单价格';
COMMENT ON COLUMN l2_trade_data.buy_order_id IS '买单ID';
COMMENT ON COLUMN l2_trade_data.buy_order_price IS '买单价格';
COMMENT ON COLUMN l2_trade_data.stock_code IS '股票代码';
COMMENT ON COLUMN l2_trade_data.trade_date IS '交易日期';
