/Users/<USER>/code/quantitativetrading/src/main/java/com/quantitative/trading/config/DatabaseConfig.java
/Users/<USER>/code/quantitativetrading/src/main/java/com/quantitative/trading/DatabaseConnectionTest.java
/Users/<USER>/code/quantitativetrading/src/main/java/com/quantitative/trading/service/L2TradeDataService.java
/Users/<USER>/code/quantitativetrading/src/main/java/com/quantitative/trading/model/L2DataValidation.java
/Users/<USER>/code/quantitativetrading/src/main/java/com/quantitative/trading/util/CsvReader.java
/Users/<USER>/code/quantitativetrading/src/main/java/com/quantitative/trading/QuantitativeTradingApplication.java
/Users/<USER>/code/quantitativetrading/src/main/java/com/quantitative/trading/model/L2ImportLog.java
/Users/<USER>/code/quantitativetrading/src/main/java/com/quantitative/trading/mapper/L2TradeDataMapper.java
/Users/<USER>/code/quantitativetrading/src/main/java/com/quantitative/trading/model/L2TradeData.java
/Users/<USER>/code/quantitativetrading/src/main/java/com/quantitative/trading/CsvOnlyDemo.java
