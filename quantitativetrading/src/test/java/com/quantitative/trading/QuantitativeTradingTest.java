package com.quantitative.trading;

import com.quantitative.trading.config.DatabaseConfig;
import com.quantitative.trading.model.L2TradeData;
import com.quantitative.trading.service.L2TradeDataService;
import com.quantitative.trading.util.CsvReader;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.AfterEach;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDate;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 量化交易系统测试类
 */
public class QuantitativeTradingTest {
    
    private static final Logger logger = LoggerFactory.getLogger(QuantitativeTradingTest.class);
    private L2TradeDataService tradeDataService;
    private static final String TEST_STOCK_CODE = "601127";
    private static final String TEST_CSV_PATH = "/Users/<USER>/l2data/2025-08-11/601127.csv";
    
    @BeforeEach
    void setUp() {
        tradeDataService = new L2TradeDataService();
        logger.info("测试环境初始化完成");
    }
    
    @AfterEach
    void tearDown() {
        // 清理测试数据
        try {
            tradeDataService.deleteByStockCodeAndDate(TEST_STOCK_CODE, LocalDate.now());
            logger.info("测试数据清理完成");
        } catch (Exception e) {
            logger.warn("清理测试数据失败", e);
        }
    }
    
    @Test
    void testCsvReaderValidation() {
        logger.info("测试CSV文件格式验证");
        
        boolean isValid = CsvReader.validateCsvFormat(TEST_CSV_PATH);
        assertTrue(isValid, "CSV文件格式应该有效");
        
        logger.info("CSV文件格式验证测试通过");
    }
    
    @Test
    void testCsvDataReading() {
        logger.info("测试CSV数据读取");
        
        List<L2TradeData> tradeDataList = CsvReader.readL2TradeData(TEST_CSV_PATH, TEST_STOCK_CODE);
        
        assertNotNull(tradeDataList, "交易数据列表不应为null");
        assertFalse(tradeDataList.isEmpty(), "交易数据列表不应为空");
        
        // 验证第一条数据
        L2TradeData firstData = tradeDataList.get(0);
        assertNotNull(firstData.getTranId(), "交易ID不应为null");
        assertNotNull(firstData.getTradeTime(), "交易时间不应为null");
        assertNotNull(firstData.getPrice(), "价格不应为null");
        assertNotNull(firstData.getVolume(), "成交量不应为null");
        assertEquals(TEST_STOCK_CODE, firstData.getStockCode(), "股票代码应该匹配");
        
        logger.info("CSV数据读取测试通过，读取{}条记录", tradeDataList.size());
    }
    
    @Test
    void testDatabaseConnection() {
        logger.info("测试数据库连接");
        
        try {
            long count = tradeDataService.countByStockCode(TEST_STOCK_CODE);
            logger.info("数据库连接测试通过，当前{}有{}条记录", TEST_STOCK_CODE, count);
        } catch (Exception e) {
            fail("数据库连接失败: " + e.getMessage());
        }
    }
    
    @Test
    void testDataSaveAndQuery() {
        logger.info("测试数据保存和查询");
        
        // 1. 读取CSV数据
        List<L2TradeData> tradeDataList = CsvReader.readL2TradeData(TEST_CSV_PATH, TEST_STOCK_CODE);
        assertTrue(tradeDataList.size() > 0, "应该读取到交易数据");
        
        // 2. 保存数据
        int savedCount = tradeDataService.batchSave(tradeDataList);
        assertEquals(tradeDataList.size(), savedCount, "保存的记录数应该与读取的记录数一致");
        
        // 3. 查询验证
        long totalCount = tradeDataService.countByStockCode(TEST_STOCK_CODE);
        assertTrue(totalCount >= savedCount, "数据库中的记录数应该不少于保存的记录数");
        
        // 4. 按日期查询
        List<L2TradeData> todayData = tradeDataService.getByStockCodeAndDate(TEST_STOCK_CODE, LocalDate.now());
        assertFalse(todayData.isEmpty(), "今日数据不应为空");
        
        logger.info("数据保存和查询测试通过，保存{}条，查询到{}条", savedCount, todayData.size());
    }
    
    @Test
    void testCompleteWorkflow() {
        logger.info("测试完整工作流程");
        
        try {
            // 执行完整的数据处理流程
            QuantitativeTradingApplication.processStockData(TEST_STOCK_CODE, TEST_CSV_PATH);
            
            // 验证结果
            long finalCount = tradeDataService.countByStockCode(TEST_STOCK_CODE);
            assertTrue(finalCount > 0, "处理完成后应该有数据");
            
            logger.info("完整工作流程测试通过，最终数据库中有{}条记录", finalCount);
            
        } catch (Exception e) {
            fail("完整工作流程测试失败: " + e.getMessage());
        }
    }
    
    @Test
    void testDataIntegrity() {
        logger.info("测试数据完整性");
        
        // 读取并保存数据
        List<L2TradeData> originalData = CsvReader.readL2TradeData(TEST_CSV_PATH, TEST_STOCK_CODE);
        tradeDataService.batchSave(originalData);
        
        // 从数据库查询数据
        List<L2TradeData> savedData = tradeDataService.getByStockCodeAndDate(TEST_STOCK_CODE, LocalDate.now());
        
        // 验证数据完整性
        assertEquals(originalData.size(), savedData.size(), "保存前后数据条数应该一致");
        
        // 验证第一条数据的关键字段
        L2TradeData original = originalData.get(0);
        L2TradeData saved = savedData.get(0);
        
        assertEquals(original.getTranId(), saved.getTranId(), "交易ID应该一致");
        assertEquals(original.getPrice(), saved.getPrice(), "价格应该一致");
        assertEquals(original.getVolume(), saved.getVolume(), "成交量应该一致");
        assertEquals(original.getStockCode(), saved.getStockCode(), "股票代码应该一致");
        
        logger.info("数据完整性测试通过");
    }
}
