-- 防重复写入的数据库表结构升级脚本

-- 1. 添加唯一约束，防止重复数据
-- 基于交易ID、股票代码、交易日期的组合唯一性
ALTER TABLE l2_trade_data 
ADD CONSTRAINT uk_l2_trade_unique 
UNIQUE (tran_id, stock_code, trade_date);

-- 2. 创建复合索引，提高查询性能
CREATE INDEX IF NOT EXISTS idx_l2_trade_composite 
ON l2_trade_data(stock_code, trade_date, tran_id);

-- 3. 创建数据导入状态表，跟踪导入历史
CREATE TABLE IF NOT EXISTS l2_import_log (
    id BIGSERIAL PRIMARY KEY,
    stock_code VARCHAR(10) NOT NULL,
    trade_date DATE NOT NULL,
    file_path VARCHAR(500),
    file_size BIGINT,
    record_count INTEGER,
    import_status VARCHAR(20) DEFAULT 'PROCESSING', -- PROCESSING, SUCCESS, FAILED, PARTIAL
    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 为导入日志表创建索引
CREATE INDEX IF NOT EXISTS idx_import_log_stock_date 
ON l2_import_log(stock_code, trade_date);

CREATE INDEX IF NOT EXISTS idx_import_log_status 
ON l2_import_log(import_status);

-- 4. 创建数据校验表，存储数据完整性检查结果
CREATE TABLE IF NOT EXISTS l2_data_validation (
    id BIGSERIAL PRIMARY KEY,
    stock_code VARCHAR(10) NOT NULL,
    trade_date DATE NOT NULL,
    total_records INTEGER,
    duplicate_records INTEGER,
    invalid_records INTEGER,
    price_range_min DECIMAL(10,4),
    price_range_max DECIMAL(10,4),
    time_range_start TIME,
    time_range_end TIME,
    validation_status VARCHAR(20) DEFAULT 'VALID', -- VALID, WARNING, ERROR
    validation_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notes TEXT
);

-- 为数据校验表创建索引
CREATE INDEX IF NOT EXISTS idx_validation_stock_date 
ON l2_data_validation(stock_code, trade_date);

-- 添加表注释
COMMENT ON TABLE l2_import_log IS 'L2数据导入日志表';
COMMENT ON TABLE l2_data_validation IS 'L2数据校验结果表';

COMMENT ON COLUMN l2_import_log.import_status IS '导入状态: PROCESSING-处理中, SUCCESS-成功, FAILED-失败, PARTIAL-部分成功';
COMMENT ON COLUMN l2_data_validation.validation_status IS '校验状态: VALID-有效, WARNING-警告, ERROR-错误';
