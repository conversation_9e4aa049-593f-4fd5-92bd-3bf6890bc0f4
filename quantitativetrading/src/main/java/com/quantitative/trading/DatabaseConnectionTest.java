package com.quantitative.trading;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;

/**
 * 简单的数据库连接测试
 */
public class DatabaseConnectionTest {
    
    public static void main(String[] args) {
        // 尝试多个连接地址
        String[] urls = {
            "*************************************************",
                "**********************************************************",
            "*********************************************",
            "**********************************************"
        };
        String username = "postgres";
        String password = "postgres123";
        
        System.out.println("开始测试数据库连接...");

        Connection connection = null;
        String successUrl = null;

        // 尝试不同的连接地址
        for (String url : urls) {
            try {
                System.out.println("尝试连接: " + url);

                // 加载PostgreSQL驱动
                Class.forName("org.postgresql.Driver");

                // 建立连接
                connection = DriverManager.getConnection(url, username, password);
                successUrl = url;
                System.out.println("数据库连接成功！使用地址: " + url);
                break;

            } catch (Exception e) {
                System.out.println("连接失败: " + url + " - " + e.getMessage());
                continue;
            }
        }

        if (connection == null) {
            System.err.println("所有连接地址都失败了！");
            return;
        }

        try {

            
            // 执行简单查询
            Statement statement = connection.createStatement();
            ResultSet resultSet = statement.executeQuery("SELECT version()");
            
            if (resultSet.next()) {
                System.out.println("数据库版本: " + resultSet.getString(1));
            }
            
            // 检查表是否存在
            ResultSet tables = statement.executeQuery(
                "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'l2_trade_data'"
            );
            
            if (tables.next()) {
                System.out.println("l2_trade_data表已存在");
                
                // 查询表结构
                ResultSet columns = statement.executeQuery(
                    "SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'l2_trade_data' ORDER BY ordinal_position"
                );
                
                System.out.println("表结构:");
                while (columns.next()) {
                    System.out.println("  " + columns.getString("column_name") + " - " + columns.getString("data_type"));
                }
                columns.close();
                
                // 查询记录数
                ResultSet count = statement.executeQuery("SELECT COUNT(*) FROM l2_trade_data");
                if (count.next()) {
                    System.out.println("当前记录数: " + count.getInt(1));
                }
                count.close();
                
            } else {
                System.out.println("l2_trade_data表不存在");
            }
            
            tables.close();
            resultSet.close();
            statement.close();
            connection.close();
            
            System.out.println("数据库连接测试完成！");
            
        } catch (Exception e) {
            System.err.println("数据库连接测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
