package com.quantitative.trading.model;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * L2数据校验结果实体类
 */
public class L2DataValidation {
    
    private Long id;
    private String stockCode;
    private LocalDate tradeDate;
    private Integer totalRecords;
    private Integer duplicateRecords;
    private Integer invalidRecords;
    private BigDecimal priceRangeMin;
    private BigDecimal priceRangeMax;
    private LocalTime timeRangeStart;
    private LocalTime timeRangeEnd;
    private ValidationStatus validationStatus;
    private LocalDateTime validationTime;
    private String notes;

    // 校验状态枚举
    public enum ValidationStatus {
        VALID("VALID", "有效"),
        WARNING("WARNING", "警告"),
        ERROR("ERROR", "错误");

        private final String code;
        private final String description;

        ValidationStatus(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static ValidationStatus fromCode(String code) {
            for (ValidationStatus status : values()) {
                if (status.code.equals(code)) {
                    return status;
                }
            }
            return VALID;
        }
    }

    // 构造函数
    public L2DataValidation() {}

    public L2DataValidation(String stockCode, LocalDate tradeDate) {
        this.stockCode = stockCode;
        this.tradeDate = tradeDate;
        this.validationStatus = ValidationStatus.VALID;
        this.validationTime = LocalDateTime.now();
    }

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getStockCode() {
        return stockCode;
    }

    public void setStockCode(String stockCode) {
        this.stockCode = stockCode;
    }

    public LocalDate getTradeDate() {
        return tradeDate;
    }

    public void setTradeDate(LocalDate tradeDate) {
        this.tradeDate = tradeDate;
    }

    public Integer getTotalRecords() {
        return totalRecords;
    }

    public void setTotalRecords(Integer totalRecords) {
        this.totalRecords = totalRecords;
    }

    public Integer getDuplicateRecords() {
        return duplicateRecords;
    }

    public void setDuplicateRecords(Integer duplicateRecords) {
        this.duplicateRecords = duplicateRecords;
    }

    public Integer getInvalidRecords() {
        return invalidRecords;
    }

    public void setInvalidRecords(Integer invalidRecords) {
        this.invalidRecords = invalidRecords;
    }

    public BigDecimal getPriceRangeMin() {
        return priceRangeMin;
    }

    public void setPriceRangeMin(BigDecimal priceRangeMin) {
        this.priceRangeMin = priceRangeMin;
    }

    public BigDecimal getPriceRangeMax() {
        return priceRangeMax;
    }

    public void setPriceRangeMax(BigDecimal priceRangeMax) {
        this.priceRangeMax = priceRangeMax;
    }

    public LocalTime getTimeRangeStart() {
        return timeRangeStart;
    }

    public void setTimeRangeStart(LocalTime timeRangeStart) {
        this.timeRangeStart = timeRangeStart;
    }

    public LocalTime getTimeRangeEnd() {
        return timeRangeEnd;
    }

    public void setTimeRangeEnd(LocalTime timeRangeEnd) {
        this.timeRangeEnd = timeRangeEnd;
    }

    public ValidationStatus getValidationStatus() {
        return validationStatus;
    }

    public void setValidationStatus(ValidationStatus validationStatus) {
        this.validationStatus = validationStatus;
    }

    public LocalDateTime getValidationTime() {
        return validationTime;
    }

    public void setValidationTime(LocalDateTime validationTime) {
        this.validationTime = validationTime;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    /**
     * 计算有效记录数
     */
    public Integer getValidRecords() {
        if (totalRecords == null) return 0;
        int invalid = (invalidRecords != null ? invalidRecords : 0);
        int duplicate = (duplicateRecords != null ? duplicateRecords : 0);
        return totalRecords - invalid - duplicate;
    }

    /**
     * 计算数据质量分数（0-100）
     */
    public double getQualityScore() {
        if (totalRecords == null || totalRecords == 0) return 0.0;
        
        int validCount = getValidRecords();
        return (validCount * 100.0) / totalRecords;
    }

    /**
     * 判断是否需要警告
     */
    public boolean needsWarning() {
        double qualityScore = getQualityScore();
        return qualityScore < 95.0 || (duplicateRecords != null && duplicateRecords > 0);
    }

    /**
     * 判断是否有错误
     */
    public boolean hasError() {
        double qualityScore = getQualityScore();
        return qualityScore < 80.0 || (invalidRecords != null && invalidRecords > totalRecords * 0.1);
    }

    @Override
    public String toString() {
        return "L2DataValidation{" +
                "id=" + id +
                ", stockCode='" + stockCode + '\'' +
                ", tradeDate=" + tradeDate +
                ", totalRecords=" + totalRecords +
                ", duplicateRecords=" + duplicateRecords +
                ", invalidRecords=" + invalidRecords +
                ", priceRangeMin=" + priceRangeMin +
                ", priceRangeMax=" + priceRangeMax +
                ", timeRangeStart=" + timeRangeStart +
                ", timeRangeEnd=" + timeRangeEnd +
                ", validationStatus=" + validationStatus +
                ", validationTime=" + validationTime +
                ", notes='" + notes + '\'' +
                '}';
    }
}
