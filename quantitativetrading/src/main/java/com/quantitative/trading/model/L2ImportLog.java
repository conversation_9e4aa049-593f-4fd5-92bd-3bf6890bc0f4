package com.quantitative.trading.model;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * L2数据导入日志实体类
 */
public class L2ImportLog {
    
    private Long id;
    private String stockCode;
    private LocalDate tradeDate;
    private String filePath;
    private Long fileSize;
    private Integer recordCount;
    private ImportStatus importStatus;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private String errorMessage;
    private LocalDateTime createdAt;

    // 导入状态枚举
    public enum ImportStatus {
        PROCESSING("PROCESSING", "处理中"),
        SUCCESS("SUCCESS", "成功"),
        FAILED("FAILED", "失败"),
        PARTIAL("PARTIAL", "部分成功");

        private final String code;
        private final String description;

        ImportStatus(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static ImportStatus fromCode(String code) {
            for (ImportStatus status : values()) {
                if (status.code.equals(code)) {
                    return status;
                }
            }
            return PROCESSING;
        }
    }

    // 构造函数
    public L2ImportLog() {}

    public L2ImportLog(String stockCode, LocalDate tradeDate, String filePath) {
        this.stockCode = stockCode;
        this.tradeDate = tradeDate;
        this.filePath = filePath;
        this.importStatus = ImportStatus.PROCESSING;
        this.startTime = LocalDateTime.now();
    }

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getStockCode() {
        return stockCode;
    }

    public void setStockCode(String stockCode) {
        this.stockCode = stockCode;
    }

    public LocalDate getTradeDate() {
        return tradeDate;
    }

    public void setTradeDate(LocalDate tradeDate) {
        this.tradeDate = tradeDate;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public Long getFileSize() {
        return fileSize;
    }

    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }

    public Integer getRecordCount() {
        return recordCount;
    }

    public void setRecordCount(Integer recordCount) {
        this.recordCount = recordCount;
    }

    public ImportStatus getImportStatus() {
        return importStatus;
    }

    public void setImportStatus(ImportStatus importStatus) {
        this.importStatus = importStatus;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    /**
     * 标记导入完成
     */
    public void markCompleted(ImportStatus status, Integer recordCount, String errorMessage) {
        this.importStatus = status;
        this.recordCount = recordCount;
        this.errorMessage = errorMessage;
        this.endTime = LocalDateTime.now();
    }

    /**
     * 计算导入耗时（秒）
     */
    public long getDurationSeconds() {
        if (startTime == null) return 0;
        LocalDateTime end = endTime != null ? endTime : LocalDateTime.now();
        return java.time.Duration.between(startTime, end).getSeconds();
    }

    @Override
    public String toString() {
        return "L2ImportLog{" +
                "id=" + id +
                ", stockCode='" + stockCode + '\'' +
                ", tradeDate=" + tradeDate +
                ", filePath='" + filePath + '\'' +
                ", fileSize=" + fileSize +
                ", recordCount=" + recordCount +
                ", importStatus=" + importStatus +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", errorMessage='" + errorMessage + '\'' +
                ", createdAt=" + createdAt +
                '}';
    }
}
