package com.quantitative.trading.model;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.LocalDateTime;

/**
 * L2交易数据实体类
 * 对应CSV文件字段：TranID,Time,Price,Volume,SaleOrderVolume,BuyOrderVolume,Type,SaleOrderID,SaleOrderPrice,BuyOrderID,BuyOrderPrice
 */
public class L2TradeData {
    
    private Long id;
    private Long tranId;
    private LocalTime tradeTime;
    private BigDecimal price;
    private Integer volume;
    private Integer saleOrderVolume;
    private Integer buyOrderVolume;
    private String tradeType;
    private Long saleOrderId;
    private BigDecimal saleOrderPrice;
    private Long buyOrderId;
    private BigDecimal buyOrderPrice;
    private String stockCode;
    private LocalDate tradeDate;
    private LocalDateTime createdAt;

    // 默认构造函数
    public L2TradeData() {}

    // 构造函数（用于从CSV创建对象）
    public L2TradeData(Long tranId, LocalTime tradeTime, BigDecimal price, Integer volume,
                       Integer saleOrderVolume, Integer buyOrderVolume, String tradeType,
                       Long saleOrderId, BigDecimal saleOrderPrice, Long buyOrderId,
                       BigDecimal buyOrderPrice, String stockCode) {
        this.tranId = tranId;
        this.tradeTime = tradeTime;
        this.price = price;
        this.volume = volume;
        this.saleOrderVolume = saleOrderVolume;
        this.buyOrderVolume = buyOrderVolume;
        this.tradeType = tradeType;
        this.saleOrderId = saleOrderId;
        this.saleOrderPrice = saleOrderPrice;
        this.buyOrderId = buyOrderId;
        this.buyOrderPrice = buyOrderPrice;
        this.stockCode = stockCode;
        this.tradeDate = LocalDate.now();
    }

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTranId() {
        return tranId;
    }

    public void setTranId(Long tranId) {
        this.tranId = tranId;
    }

    public LocalTime getTradeTime() {
        return tradeTime;
    }

    public void setTradeTime(LocalTime tradeTime) {
        this.tradeTime = tradeTime;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public Integer getVolume() {
        return volume;
    }

    public void setVolume(Integer volume) {
        this.volume = volume;
    }

    public Integer getSaleOrderVolume() {
        return saleOrderVolume;
    }

    public void setSaleOrderVolume(Integer saleOrderVolume) {
        this.saleOrderVolume = saleOrderVolume;
    }

    public Integer getBuyOrderVolume() {
        return buyOrderVolume;
    }

    public void setBuyOrderVolume(Integer buyOrderVolume) {
        this.buyOrderVolume = buyOrderVolume;
    }

    public String getTradeType() {
        return tradeType;
    }

    public void setTradeType(String tradeType) {
        this.tradeType = tradeType;
    }

    public Long getSaleOrderId() {
        return saleOrderId;
    }

    public void setSaleOrderId(Long saleOrderId) {
        this.saleOrderId = saleOrderId;
    }

    public BigDecimal getSaleOrderPrice() {
        return saleOrderPrice;
    }

    public void setSaleOrderPrice(BigDecimal saleOrderPrice) {
        this.saleOrderPrice = saleOrderPrice;
    }

    public Long getBuyOrderId() {
        return buyOrderId;
    }

    public void setBuyOrderId(Long buyOrderId) {
        this.buyOrderId = buyOrderId;
    }

    public BigDecimal getBuyOrderPrice() {
        return buyOrderPrice;
    }

    public void setBuyOrderPrice(BigDecimal buyOrderPrice) {
        this.buyOrderPrice = buyOrderPrice;
    }

    public String getStockCode() {
        return stockCode;
    }

    public void setStockCode(String stockCode) {
        this.stockCode = stockCode;
    }

    public LocalDate getTradeDate() {
        return tradeDate;
    }

    public void setTradeDate(LocalDate tradeDate) {
        this.tradeDate = tradeDate;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    @Override
    public String toString() {
        return "L2TradeData{" +
                "id=" + id +
                ", tranId=" + tranId +
                ", tradeTime=" + tradeTime +
                ", price=" + price +
                ", volume=" + volume +
                ", saleOrderVolume=" + saleOrderVolume +
                ", buyOrderVolume=" + buyOrderVolume +
                ", tradeType='" + tradeType + '\'' +
                ", saleOrderId=" + saleOrderId +
                ", saleOrderPrice=" + saleOrderPrice +
                ", buyOrderId=" + buyOrderId +
                ", buyOrderPrice=" + buyOrderPrice +
                ", stockCode='" + stockCode + '\'' +
                ", tradeDate=" + tradeDate +
                ", createdAt=" + createdAt +
                '}';
    }
}
