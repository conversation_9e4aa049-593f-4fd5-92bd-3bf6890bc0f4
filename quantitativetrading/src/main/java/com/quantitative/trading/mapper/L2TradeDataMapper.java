package com.quantitative.trading.mapper;

import com.quantitative.trading.model.L2TradeData;
import com.quantitative.trading.model.L2ImportLog;
import com.quantitative.trading.model.L2DataValidation;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import java.time.LocalDate;

/**
 * L2交易数据Mapper接口
 */
public interface L2TradeDataMapper {
    
    /**
     * 插入单条L2交易数据
     * @param l2TradeData L2交易数据
     * @return 影响行数
     */
    int insert(L2TradeData l2TradeData);
    
    /**
     * 批量插入L2交易数据
     * @param l2TradeDataList L2交易数据列表
     * @return 影响行数
     */
    int batchInsert(List<L2TradeData> l2TradeDataList);
    
    /**
     * 根据ID查询L2交易数据
     * @param id 主键ID
     * @return L2交易数据
     */
    L2TradeData selectById(Long id);
    
    /**
     * 根据股票代码查询L2交易数据
     * @param stockCode 股票代码
     * @return L2交易数据列表
     */
    List<L2TradeData> selectByStockCode(@Param("stockCode") String stockCode);

    /**
     * 根据股票代码和交易日期查询L2交易数据
     * @param stockCode 股票代码
     * @param tradeDate 交易日期
     * @return L2交易数据列表
     */
    List<L2TradeData> selectByStockCodeAndDate(@Param("stockCode") String stockCode, @Param("tradeDate") LocalDate tradeDate);

    /**
     * 查询指定股票代码的交易数据总数
     * @param stockCode 股票代码
     * @return 总数
     */
    long countByStockCode(@Param("stockCode") String stockCode);
    
    /**
     * 删除指定股票代码和日期的数据
     * @param stockCode 股票代码
     * @param tradeDate 交易日期
     * @return 影响行数
     */
    int deleteByStockCodeAndDate(String stockCode, LocalDate tradeDate);

    /**
     * 检查数据是否已存在（防重复）
     * @param tranId 交易ID
     * @param stockCode 股票代码
     * @param tradeDate 交易日期
     * @return 存在的记录数
     */
    int checkDataExists(Long tranId, String stockCode, LocalDate tradeDate);

    /**
     * 批量插入（忽略重复数据）
     * @param l2TradeDataList L2交易数据列表
     * @return 影响行数
     */
    int batchInsertIgnoreDuplicate(List<L2TradeData> l2TradeDataList);

    /**
     * 获取重复数据统计
     * @param stockCode 股票代码
     * @param tradeDate 交易日期
     * @return 重复记录数
     */
    int countDuplicateRecords(String stockCode, LocalDate tradeDate);

    // === 导入日志相关方法 ===

    /**
     * 插入导入日志
     * @param importLog 导入日志
     * @return 影响行数
     */
    int insertImportLog(L2ImportLog importLog);

    /**
     * 更新导入日志
     * @param importLog 导入日志
     * @return 影响行数
     */
    int updateImportLog(L2ImportLog importLog);

    /**
     * 查询导入日志
     * @param stockCode 股票代码
     * @param tradeDate 交易日期
     * @return 导入日志列表
     */
    List<L2ImportLog> selectImportLogs(String stockCode, LocalDate tradeDate);

    /**
     * 检查是否已导入
     * @param stockCode 股票代码
     * @param tradeDate 交易日期
     * @return 成功导入的记录数
     */
    int checkImportExists(String stockCode, LocalDate tradeDate);

    // === 数据校验相关方法 ===

    /**
     * 插入数据校验结果
     * @param validation 校验结果
     * @return 影响行数
     */
    int insertDataValidation(L2DataValidation validation);

    /**
     * 查询数据校验结果
     * @param stockCode 股票代码
     * @param tradeDate 交易日期
     * @return 校验结果
     */
    L2DataValidation selectDataValidation(String stockCode, LocalDate tradeDate);
}
