package com.quantitative.trading;

import com.quantitative.trading.config.DatabaseConfig;
import com.quantitative.trading.model.L2TradeData;
import com.quantitative.trading.service.L2TradeDataService;
import com.quantitative.trading.util.CsvReader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDate;
import java.util.List;

/**
 * 量化交易系统主程序
 */
public class QuantitativeTradingApplication {
    
    private static final Logger logger = LoggerFactory.getLogger(QuantitativeTradingApplication.class);
    
    public static void main(String[] args) {
        logger.info("量化交易系统启动...");
        
        try {
            // 配置参数
            String stockCode = "601127"; // 赛力斯股票代码
            String csvFilePath = "/Users/<USER>/l2data/2025-08-11/" + stockCode + ".csv";
            
            // 创建服务实例
            L2TradeDataService tradeDataService = new L2TradeDataService();
            
            // 1. 验证CSV文件格式
            logger.info("验证CSV文件格式: {}", csvFilePath);
            if (!CsvReader.validateCsvFormat(csvFilePath)) {
                logger.error("CSV文件格式不正确，程序退出");
                return;
            }
            logger.info("CSV文件格式验证通过");
            
            // 2. 读取CSV数据
            logger.info("开始读取{}的L2交易数据...", stockCode);
            List<L2TradeData> tradeDataList = CsvReader.readL2TradeData(csvFilePath, stockCode);
            
            if (tradeDataList.isEmpty()) {
                logger.warn("未读取到任何交易数据，程序退出");
                return;
            }
            
            logger.info("成功读取{}条L2交易数据", tradeDataList.size());
            
            // 3. 检查是否已存在当日数据，如果存在则删除（避免重复导入）
            LocalDate today = LocalDate.now();
            long existingCount = tradeDataService.countByStockCode(stockCode);
            if (existingCount > 0) {
                logger.info("发现{}已存在{}条交易数据，删除后重新导入", stockCode, existingCount);
                tradeDataService.deleteByStockCodeAndDate(stockCode, today);
            }
            
            // 4. 批量保存到数据库
            logger.info("开始批量保存L2交易数据到数据库...");
            int savedCount = tradeDataService.batchSave(tradeDataList);
            
            // 5. 验证保存结果
            long finalCount = tradeDataService.countByStockCode(stockCode);
            logger.info("数据保存完成！保存{}条记录，数据库中{}共有{}条记录", 
                       savedCount, stockCode, finalCount);
            
            // 6. 显示部分数据样例
            logger.info("查询前5条数据作为样例:");
            List<L2TradeData> sampleData = tradeDataService.getByStockCodeAndDate(stockCode, today);
            for (int i = 0; i < Math.min(5, sampleData.size()); i++) {
                L2TradeData data = sampleData.get(i);
                logger.info("样例数据{}: 交易ID={}, 时间={}, 价格={}, 成交量={}, 类型={}", 
                           i + 1, data.getTranId(), data.getTradeTime(), 
                           data.getPrice(), data.getVolume(), data.getTradeType());
            }
            
            logger.info("量化交易系统处理完成！");
            
        } catch (Exception e) {
            logger.error("程序执行失败", e);
        } finally {
            // 关闭数据源
            DatabaseConfig.closeDataSource();
            logger.info("量化交易系统已关闭");
        }
    }
    
    /**
     * 处理指定股票的L2数据
     * @param stockCode 股票代码
     * @param csvFilePath CSV文件路径
     */
    public static void processStockData(String stockCode, String csvFilePath) {
        logger.info("开始处理股票{}的L2数据: {}", stockCode, csvFilePath);
        
        try {
            L2TradeDataService tradeDataService = new L2TradeDataService();
            
            // 读取CSV数据
            List<L2TradeData> tradeDataList = CsvReader.readL2TradeData(csvFilePath, stockCode);
            
            if (!tradeDataList.isEmpty()) {
                // 删除已存在的当日数据
                tradeDataService.deleteByStockCodeAndDate(stockCode, LocalDate.now());
                
                // 批量保存
                int savedCount = tradeDataService.batchSave(tradeDataList);
                logger.info("股票{}处理完成，保存{}条记录", stockCode, savedCount);
            } else {
                logger.warn("股票{}未读取到任何数据", stockCode);
            }
            
        } catch (Exception e) {
            logger.error("处理股票{}的数据失败", stockCode, e);
        }
    }
}
